{%- doc -%}
  Renders the cart drawer, a slide-out panel that displays the contents of the cart. It includes the cart icon that acts as a trigger.

  @param {object} [settings] - An object containing theme settings.

  @param {boolean} [settings.auto_open_cart_drawer] - If `true`, the cart drawer opens automatically after an item is
  added.
  @param {string} [settings.drawer_color_scheme] - The color scheme for the drawer.
{%- enddoc -%}

<script
  src="{{ 'cart-drawer.js' | asset_url }}"
  type="module"
></script>

<cart-drawer-component
  class="cart-drawer"
  {{ block.shopify_attributes }}
  {% if settings.auto_open_cart_drawer %}
    auto-open
  {% endif %}
>
  <button
    class="button header-actions__action button-unstyled"
    on:click="/open"
    aria-label="{{ 'accessibility.open_cart_drawer' | t }} {{ 'accessibility.cart_count' | t}}: {{ cart.item_count }}"
  >
    {% render 'cart-icon-component' %}
  </button>

  <dialog
    ref="dialog"
    class="cart-drawer__dialog dialog-modal dialog-drawer color-{{ settings.drawer_color_scheme }}{% if cart.empty? %} cart-drawer--empty{%endif%}"
    scroll-lock
  >
    <div class="cart-drawer__inner">
      <cart-items-component
        class="cart-items-component"
        data-section-id="{{ section.id }}"
      >
        {%- if cart.empty? -%}
          <div class="cart-drawer__header">
            <button
              ref="closeButton"
              on:click="cart-drawer-component/close"
              class="cart-drawer__close-button button-unstyled"
              aria-label="{{ 'actions.close_dialog' | t }}"
            >
              <span class="svg-wrapper">
                <img src="https://cdn.shopify.com/s/files/1/0714/5881/6158/files/Vector_2.svg?v=1752746719" class="close-icon">
              </span>
            </button>
          </div>

          <div
            class="cart-drawer__content motion-reduce"
            aria-label="{{ 'accessibility.cart' | t }}"
          >
            <span class="cart-drawer__heading h3 cart-drawer__heading--empty">
              {{ 'content.your_cart_is_empty' | t }}
            </span>

            <div class="cart-drawer__items">
              {% render 'cart-products' %}
            </div>
          </div>
        {%- else -%}
          <div
            class="cart-drawer__header"
            id="cart-drawer-header"
          >
            <span class="cart-drawer__heading h3">
              {{ 'content.cart_title' | t }}
             {% comment %} {% render 'cart-bubble' %} {% endcomment %}
            </span>

            <button
              ref="closeButton"
              on:click="cart-drawer-component/close"
              class="cart-drawer__close-button button-unstyled"
              aria-label="{{ 'actions.close_dialog' | t }}"
            >
              <span class="svg-wrapper">
                <img src="https://cdn.shopify.com/s/files/1/0714/5881/6158/files/Vector_2.svg?v=1752746719" class="close-icon">
              </span>
            </button>
          </div>

          <div
            class="cart-drawer__content motion-reduce"
            aria-label="{{ 'accessibility.cart' | t }}"
            style="--header-height: 60px;"
          >
            <scroll-hint
              class="cart-drawer__items"
            >
              {% render 'cart-products' %}
            </scroll-hint>

            <div
              class="cart-drawer__summary"
            >
              {% render 'cart-summary' %}
            </div>
          </div>
        {%- endif -%}
      </cart-items-component>
    </div>
  </dialog>
</cart-drawer-component>

{% stylesheet %}
  .cart-items-component {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .cart-drawer__heading .cart-bubble {
    width: fit-content;
    border-radius: var(--style-border-radius-buttons-primary);
    aspect-ratio: auto;
    padding: var(--cart-padding);
  }

  .cart-drawer__heading .cart-bubble[data-maintain-ratio] {
    aspect-ratio: 1;
    min-width: 26px;
  }

  .cart-drawer__header {
    background-color: var(--color-background);
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: var(--cart-drawer-padding);
    border-bottom: var(--style-border-width) solid none;
    position: sticky;
    top: 0;
    z-index: 1;

    @media screen and (width >= 750px) {
      padding: var(--cart-drawer-padding-desktop);
    }
  }

  .cart-drawer__dialog {
    overflow: hidden;
  }

  .cart-drawer__inner {
    height: 100%;
    overflow: hidden;
  }

  .cart-drawer__content {
    height: calc(100% - var(--header-height));
    display: flex;
    flex-direction: column;
  }

  .cart-drawer__summary {
    background-color: var(--color-background);
    position: sticky;
    bottom: 0;
    z-index: 1;
  }
   img.close-icon {
    width: 16px;
}
  span.cart-drawer__heading.h3 {
    font-size: 20px;
    font-weight: bold;
}
  button.cart-drawer__close-button.button-unstyled {
    cursor: pointer;
}
  quantity-selector-component.quantity-selector.cart-primary-typography {
    background: transparent;
    border: 1px solid #007A73;
    border-radius: 10px;
}
button.button.button--tertiary.cart-items__remove {
    position: relative;
    left: -110px;
} 
  a.cart-items__title {
    font-size: 14px;
    font-weight: bold;
}
  .cart-discount-form {
    display: flex;
    gap: 8px;
    margin-top: 16px;
}
  .cart-discount-form input {
    flex: 1;
    border-radius: 8px;
    padding: 10px;
    background: white;
    border: 1px solid #ccc;
}
  .cart-discount-form button {
    padding: 6px 15px;
    background: #EDEDED;
    color: #A5A8AD;
    border-radius: 8px;
    border: none;
    cursor: not-allowed;
    transition: all 0.3s ease;
  }
  .cart-discount-form button:disabled {
    background: #EDEDED;
    color: #A5A8AD;
    cursor: not-allowed;
  }
  .cart-discount-form button.active,
  .cart-discount-form button:not(:disabled) {
    background: #007A73;
    color: white;
    cursor: pointer;
  }
  .cart-discount-form button.active:hover,
  .cart-discount-form button:not(:disabled):hover {
    background: #005e43;
  }
  input#discount-code::placeholder {
    color: #A5A8AD !important;
}
  .cart-item__qty-badge {
  position: absolute;
  top: 0px;
  left: -10px;
  background-color: #007A73;
  color: #fff;
  font-size: 12px;
  font-weight: bold;
  padding: 3px 8px 1px 7px;
  border-radius: 50%;
  z-index: 2;
  pointer-events: none;
}
.price-per-unit {
    display: none;
}
{% endstylesheet %}
